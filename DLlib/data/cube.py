"""
MinuteCube - 核心数据容器

管理 [F, D, T, N] 格式的时间序列数据，提供视图转换和有效性掩码。
仅管理数据视图与有效性掩码，不做任何 pandas 操作。
"""

import numpy as np
from typing import Tuple, List


class MinuteCube:
    """
    仅管理数据视图与有效性掩码，不做任何 pandas 操作。
    输入: X [F, D, T, N]
    内部主视图: X_dtnf [D, T, N, F] (zero-copy view via moveaxis)
    
    参数:
        X_fdtN: 输入数组，形状为 [F, D, T, N]
            - F: 特征数（包含标签和可选权重）
            - D: 天数
            - T: 每天分钟数（固定 241）
            - N: 股票数（固定 6000，用NaN占位）
        assume_nan_invalid: 是否将NaN视为无效数据
    """
    
    def __init__(self, X_fdtN: np.ndarray, assume_nan_invalid: bool = True):
        assert X_fdtN.ndim == 4, "Expect [F, D, T, N]"
        self.X_dtnf = np.moveaxis(X_fdtN, (0,1,2,3), (3,0,1,2))  # -> [D, T, N, F]
        self.D, self.T, self.N, self.F = self.X_dtnf.shape
        self.assume_nan_invalid = assume_nan_invalid
        
        # 整天有效（若输入还有 NaN，占位将被排除）
        self.valid_dn = np.isfinite(self.X_dtnf[..., :-1]).all(axis=(1, 3))  # TODO: -1 just for one label 
        self.valid_lists: List[np.ndarray] = [
            np.nonzero(self.valid_dn[d])[0] for d in range(self.D)
        ]

    @staticmethod
    def from_fdtN(X_fdtN: np.ndarray, assume_nan_invalid: bool = True) -> "MinuteCube":
        """从 [F, D, T, N] 格式创建 MinuteCube"""
        return MinuteCube(X_fdtN, assume_nan_invalid=assume_nan_invalid)

    def day_slice(self, d: int) -> np.ndarray:
        """获取某天的数据切片 [T, N, F]"""
        return self.X_dtnf[d]  # [T, N, F]

    def minute_slice(self, d: int, t: int) -> np.ndarray:
        """获取某天某分钟的数据切片 [N, F]"""
        return self.X_dtnf[d, t]  # [N, F]

    def split_days(self, train_ratio: float = 0.8) -> Tuple["MinuteCube", "MinuteCube", np.ndarray, np.ndarray]:
        """
        按天时间顺序拆分 -> (cube_train, cube_valid, idx_train, idx_valid)
        返回的两个 cube 是"视图"（不复制）。
        """
        D_train = int(self.D * train_ratio)
        idx_train = np.arange(D_train, dtype=np.int64)
        idx_valid = np.arange(D_train, self.D, dtype=np.int64)
        
        # 视图切片（把 [D,T,N,F] 切出来再 moveaxis 回 [F,D,T,N] 供构造器使用；都是视图，不拷贝）
        X_tr_fdtN = np.moveaxis(self.X_dtnf[idx_train], (3,0,1,2), (0,1,2,3))
        X_va_fdtN = np.moveaxis(self.X_dtnf[idx_valid], (3,0,1,2), (0,1,2,3))
        
        cube_train = MinuteCube(X_tr_fdtN)
        cube_valid = MinuteCube(X_va_fdtN)
        
        return cube_train, cube_valid, idx_train, idx_valid
