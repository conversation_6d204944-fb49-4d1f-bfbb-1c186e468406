"""
Tensor 化数据集类

提供直接输出 PyTorch Tensor 的数据集类，支持标签和权重提取。
统一输出 dict 格式：{"features": Tensor, "weight": Tensor or None, "label": Tensor}
"""

import numpy as np
import torch
from typing import Dict, Optional
from torch.utils.data import Dataset

from .cube import MinuteCube
from .field_spec import FieldSpec



def _safe_std(std: np.ndarray, eps: float) -> np.ndarray:
    # 避免除 0 或 非有限
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)

def _zscore_cs_along_N(x: np.ndarray, eps: float) -> np.ndarray:
    """
    沿 N 轴做 zscore：
      适配 x 形状为 [N, T, C] 或 [N, L, C] 或 [N, 1, C]
    """
    mu = x.mean(axis=0, keepdims=True)                 # [1, T/L, C]
    sd = _safe_std(x.std(axis=0, keepdims=True), eps)  # [1, T/L, C]
    return (x - mu) / sd

def _zscore_ts_along_time(x: np.ndarray, eps: float) -> np.ndarray:
    """
    沿时间轴(第二维)做 zscore：
      仅用于特征；x 形状为 [N, L, F]
    """
    mu = x.mean(axis=1, keepdims=True)                 # [N,1,F]
    sd = _safe_std(x.std(axis=1, keepdims=True), eps)  # [N,1,F]
    return (x - mu) / sd

def _zscore_cs_labels_keep_nan(y: np.ndarray, eps: float) -> np.ndarray:
    """
    标签做截面 zscore（沿 N），用 nanmean/nanstd；原始 NaN 保留为 NaN。
      适配 y 为 [N, T, d_y] 或 [N, 1, d_y]
    """
    mask = ~np.isfinite(y)                              # True 表示原先是 NaN/Inf
    mu = np.nanmean(y, axis=0, keepdims=True)
    sd = np.nanstd (y, axis=0, keepdims=True)
    sd = _safe_std(sd, eps)
    out = (y - mu) / sd
    out[mask] = np.nan
    return out

class TensorDaySectionDataset(Dataset):
    """
    一天一个样本，输出 dict：
      features: [N_valid, T, F_feat]
      weight:   [N_valid, T, n_weight] 或 None
      label:    [N_valid, T, F_label]

    在线处理（仅支持截面 CS 标准化）：
      - 若 fillna_features_to_zero=True，则对 features 先 NaN→0（保证训练无 NaN）
      - features_norm='cs' 时，对每个 t、每个 f 沿 N 做 zscore
      - labels_norm='cs'  时，对每个 t、每个 label 通道沿 N 做 zscore（用 nanmean/nanstd，保留原 NaN）
    """
    def __init__(
        self,
        cube: MinuteCube,
        spec: FieldSpec,
        *,
        fillna_features_to_zero: bool = True,
        features_norm: str = "cs",     # 'cs' | 'none'
        labels_norm: str = "cs",       # 'cs' | 'none'
        eps: float = 1e-6,
    ):
        assert features_norm in ("cs", "none")
        assert labels_norm in ("cs", "none")
        self.cube = cube
        self.spec = spec
        self.fillna_features_to_zero = fillna_features_to_zero
        self.features_norm = features_norm
        self.labels_norm = labels_norm
        self.eps = float(eps)

    def __len__(self) -> int:
        return self.cube.D

    def __getitem__(self, d: int) -> Dict[str, torch.Tensor]:
        X_TNF = self.cube.day_slice(d)               # [T, N, F_total]
        n_idx = self.cube.valid_lists[d]             # [N_valid] —— 全局股票索引
        x = X_TNF[:, n_idx, :]                       # [T, N_valid, F_total]
        x = np.moveaxis(x, 0, 1)                     # -> [N_valid, T, F_total]

        f_np, w_np, y_np = self.spec.split_np(x)

        if self.fillna_features_to_zero:
            np.nan_to_num(f_np, copy=False, nan=0.0, posinf=0.0, neginf=0.0)
        if self.features_norm == "cs":
            f_np = _zscore_cs_along_N(f_np, eps=self.eps)
        if (y_np is not None) and (self.labels_norm == "cs"):
            y_np_cszscore = _zscore_cs_labels_keep_nan(y_np, eps=self.eps)

        f, w, y, y0 = self.spec.to_tensor(f_np, w_np, y_np_cszscore, y_np)
        return {
            "features": f,             # [N_valid, T, F_feat]
            "weight":   w,             # [N_valid, T, n_weight] 或 None
            "label":    y,             # [N_valid, T, F_label]
            "label_raw":  y0,            # [N_valid, T, F_label]
            "day_idx":  torch.tensor(d, dtype=torch.long),
            "n_idx":    torch.as_tensor(n_idx, dtype=torch.long),
        }


class TensorPairDataset(Dataset):
    """
    (day, stock) 最小单位，输出可 batch：
      features: [T, F_feat]
      weight:   [T, n_weight] 或 None
      label:    [T, F_label]
    """
    def __init__(self, cube: MinuteCube, spec: FieldSpec):
        self.cube = cube
        self.spec = spec
        dn = cube.valid_dn
        self.d_idx, self.n_idx = np.nonzero(dn)
        self.length = self.d_idx.shape[0]

    def __len__(self): 
        return self.length

    def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
        d = int(self.d_idx[i])
        n = int(self.n_idx[i])
        x = self.cube.X_dtnf[d, :, n, :]            # [T, F_total]
        f_np, w_np, y_np = self.spec.split_np(x)
        f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
        return {"features": f, "weight": w, "label": y}


class TensorMinuteLookbackWithinDayDataset(Dataset):
    pass
    # """
    # 尽量用TensorMinuteLookbackAcrossDaysDataset
    # 因为WihtinDay会导致一天的开头是一样的，没啥意义，除非是专门做尾盘模型等

    # (d,t) 锚点，同日内回看 L 分钟，输出：
    #   features: [N_valid, L, F_feat], weight 同形，label 同形
    # """
    # def __init__(self, cube: MinuteCube, spec: FieldSpec, L: int, filter_valid: bool = True):
    #     self.cube = cube
    #     self.spec = spec
    #     self.L = int(L)
    #     self.D, self.T, self.N, self.F = cube.X_dtnf.shape
    #     self.filter_valid = filter_valid
    #     self.win = np.stack([np.clip(np.arange(t - self.L + 1, t + 1, dtype=np.int64), 0, t)
    #                          for t in range(self.T)], axis=0)  # [T, L]
    #     self.length = self.D * self.T

    # def __len__(self): 
    #     return self.length

    # def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
    #     d = i // self.T
    #     t = i % self.T
    #     minutes = self.win[t]                         # [L]
    #     x = self.cube.X_dtnf[d, minutes, :, :]       # [L, N, F_total]
    #     x = np.moveaxis(x, 0, 1)                     # -> [N, L, F_total]
    #     if self.filter_valid:
    #         idx_n = self.cube.valid_lists[d]
    #         x = x[idx_n, :, :]                       # [N_valid, L, F_total]
    #     f_np, w_np, y_np = self.spec.split_np(x)
    #     f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
    #     return {"features": f, "weight": w, "label": y}


class TensorMinuteLookbackAcrossDaysDataset(Dataset):
    """
    (d,t) 跨天连续分钟回看 L，输出：
      features: [N_sel, L, F_feat]
      weight:   [N_sel, 1, n_weight] 或 None（取窗口最后一分钟）
      label:    [N_sel, 1, F_label]  （取窗口最后一分钟）

    在线处理：
      - features:
          * 填 0（可选）
          * 标准化二选一：features_norm ∈ {'none', 'cs', 'ts'}
            - 'cs': 对每个 l、每个 f 沿 N 做 zscore
            - 'ts': 对每个 n、每个 f 沿 L 做 zscore
      - labels:
          * 只支持 CS（沿 N），保留 NaN；或 'none'
    """
    def __init__(
        self,
        cube: MinuteCube,
        spec: FieldSpec,
        L: int,
        *,
        filter_mode: str = "window",
        pad_mode: str = "repeat",
        precompute_windows: bool = True,
        fillna_features_to_zero: bool = True,
        features_norm: str = "cs",      # 'none' | 'cs' | 'ts'（互斥）
        labels_norm: str = "cs",        # 'cs' | 'none'
        eps: float = 1e-6,
    ):
        assert features_norm in ("none", "cs", "ts")
        assert labels_norm in ("cs", "none")
        self.cube = cube
        self.spec = spec
        self.L = int(L)
        self.filter_mode = filter_mode
        self.pad_mode = pad_mode
        self.fillna_features_to_zero = fillna_features_to_zero
        self.features_norm = features_norm
        self.labels_norm = labels_norm
        self.eps = float(eps)

        self.D, self.T, self.N, self.F = cube.X_dtnf.shape
        self.G = self.D * self.T
        g = np.arange(self.G, dtype=np.int64)
        self.g2d = g // self.T
        self.g2t = g %  self.T

        if precompute_windows:
            base = np.arange(-self.L + 1, 1, dtype=np.int64)
            gw = g[:, None] + base[None, :]
            if pad_mode == "repeat":
                gw = np.clip(gw, 0, None)
            elif pad_mode == "nan":
                self._gw_neg_mask = (gw < 0)
                gw = np.clip(gw, 0, None)
            else:
                raise ValueError(f"pad_mode {pad_mode} not supported")
            self.g_windows = gw
        else:
            self.g_windows = None

    def __len__(self):
        return self.G

    def _window_indices(self, g: int):
        if self.g_windows is not None:
            gw = self.g_windows[g]
            neg_mask = None
            if self.pad_mode == "nan":
                neg_mask = getattr(self, "_gw_neg_mask")[g]
            return gw, neg_mask
        base = np.arange(g - self.L + 1, g + 1, dtype=np.int64)
        if self.pad_mode == "repeat":
            return np.clip(base, 0, None), None
        neg = base < 0
        return np.clip(base, 0, None), neg

    def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
        g = int(i)
        d = int(self.g2d[g])
        t = int(self.g2t[g])
        gw, neg_mask = self._window_indices(g)
        days    = (gw // self.T).astype(np.int64)
        minutes = (gw %  self.T).astype(np.int64)

        x = self.cube.X_dtnf[days, minutes, :, :]     # [L,N,F_total]
        x = np.moveaxis(x, 0, 1)                      # -> [N,L,F_total]

        if neg_mask is not None and np.any(neg_mask):
            x[:, neg_mask, :] = np.nan

        # —— 核心：确定被保留的股票全局索引 n_idx —— #
        if self.filter_mode == "anchor":
            n_idx = self.cube.valid_lists[d]                    # [N_sel]
            x = x[n_idx, :, :]                                  # [N_sel,L,F]
        elif self.filter_mode == "window":
            mask_n = self.cube.valid_dn[days].all(axis=0)       # [N]
            n_idx = np.nonzero(mask_n)[0]                       # [N_sel]
            x = x[n_idx, :, :]
        elif self.filter_mode == "none":
            n_idx = np.arange(self.N, dtype=np.int64)           # [N]
            # x 已经是 [N,L,F]
        else:
            raise ValueError(f"filter_mode {self.filter_mode} not supported")

        f_np, w_np, y_np = self.spec.split_np(x)      # f:[N,L,Ff], w:[N,L,W?] or None, y:[N,L,Fl]
        # 取最后一分钟作为标签/权重
        if w_np is not None:
            w_np = w_np[:, -1:, :]                    # -> [N,1,W?]
        if y_np is not None:
            y_np = y_np[:, -1:, :]                    # -> [N,1,Fl]

        # ---- features: NaN→0（可选）----
        if self.fillna_features_to_zero:
            np.nan_to_num(f_np, copy=False, nan=0.0, posinf=0.0, neginf=0.0)

        # ---- features: 标准化（互斥）----
        if self.features_norm == "cs":
            # 对每个 l、每个 f 沿 N 做 zscore
            f_np = _zscore_cs_along_N(f_np, eps=self.eps)
        elif self.features_norm == "ts":
            # 对每个 n、每个 f 沿 L 做 zscore
            f_np = _zscore_ts_along_time(f_np, eps=self.eps)
        # 'none'：不做

        # ---- labels: CS 标准化（可选，保留 NaN）----
        if (y_np is not None) and (self.labels_norm == "cs"):
            y_np_cszscore = _zscore_cs_labels_keep_nan(y_np, eps=self.eps)

        f, w, y, y0 = self.spec.to_tensor(f_np, w_np, y_np_cszscore, y_np)
        return {
            "features": f, "weight": w, "label": y, "label_raw": y0,    # y:[N_sel,1,Fl], 典型 Fl=1
            "day_idx": torch.tensor(d, dtype=torch.long),
            "t_idx":   torch.tensor(t, dtype=torch.long),
            "n_idx":   torch.as_tensor(n_idx, dtype=torch.long),   # [N_sel] 全局股票索引
        }
