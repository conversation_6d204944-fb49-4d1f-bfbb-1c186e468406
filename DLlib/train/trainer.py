"""
模型训练器

提供统一的训练接口，支持DDP分布式训练和早停机制。
"""

from typing import Dict, Callable, Optional, Any
from loguru import logger
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from .metrics import ddp_mean_scalar


class EarlyStopper:
    """早停机制"""
    
    def __init__(self, patience: int = 10, higher_better: bool = False):
        """
        参数:
            patience: 容忍轮数
            higher_better: 指标是否越高越好
        """
        self.patience = patience
        self.higher_better = higher_better
        self.best_value = None
        self.best_state = None
        self.count = 0

    def step(self, value: float, model: nn.Module) -> bool:
        """
        更新早停状态
        
        参数:
            value: 当前指标值
            model: 模型
        
        返回:
            是否应该停止训练
        """
        if self.best_value is None:
            self.best_value = value
            self.best_state = {k: v.detach().cpu().clone() for k, v in model.state_dict().items()}
            self.count = 0
            return False
        
        improve = (value > self.best_value) if self.higher_better else (value < self.best_value)
        if improve:
            self.best_value = value
            self.best_state = {k: v.detach().cpu().clone() for k, v in model.state_dict().items()}
            self.count = 0
            return False
        
        self.count += 1
        return self.count >= self.patience

    def restore(self, model: nn.Module):
        """恢复最佳模型状态"""
        if self.best_state is not None:
            model.load_state_dict(self.best_state)


class ModelTrainer:
    """
    模型训练器
    
    特点：
    - 极简、专业的设计
    - 支持可插拔的损失函数和评估指标
    - 兼容DDP分布式训练
    - 支持4种Dataset：DaySection/Pair/MinuteLookback
    - 自动早停和模型保存
    """
    
    def __init__(
        self,
        model: nn.Module,
        optimizer: torch.optim.Optimizer,
        *,
        loss_fn: Callable[[torch.Tensor, torch.Tensor, torch.Tensor], torch.Tensor],
        metrics: Optional[Dict[str, Callable[[torch.Tensor, torch.Tensor, torch.Tensor], torch.Tensor]]] = None,
        primary_metric: Optional[str] = None,
        primary_higher_better: bool = False,
        device: torch.device = torch.device("cpu"),
        use_ddp: bool = False,
        ddp_find_unused_parameters: bool = False,
        grad_clip_norm: Optional[float] = None,
        grad_accum_steps: int = 1,
        scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
        early_stop_patience: int = 10,
        tracker: Optional[Any] = None,
    ):
        """
        参数:
            model: 待训练模型
            optimizer: 优化器
            loss_fn: 损失函数，签名为 (pred, label, weight) -> Tensor
            metrics: 评估指标字典
            primary_metric: 主要指标名称，用于早停
            primary_higher_better: 主要指标是否越高越好
            device: 设备
            use_ddp: 是否使用DDP
            ddp_find_unused_parameters: DDP参数
            grad_clip_norm: 梯度裁剪阈值
            grad_accum_steps: 梯度累积步数
            scheduler: 学习率调度器
            early_stop_patience: 早停容忍轮数
            tracker: wandb跟踪器（可选）
        """
        self.device = device
        self.model = model.to(device)
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.loss_fn = loss_fn
        self.metrics = metrics or {}
        self.primary_metric = primary_metric
        self.early = EarlyStopper(patience=early_stop_patience, higher_better=bool(primary_higher_better))
        self.grad_clip_norm = grad_clip_norm
        self.grad_accum_steps = max(int(grad_accum_steps), 1)
        self.tracker = tracker

        self.use_ddp = use_ddp and dist.is_available() and dist.is_initialized()
        if self.use_ddp:
            self.model = DDP(
                self.model, 
                device_ids=[device.index], 
                output_device=device.index,
                find_unused_parameters=ddp_find_unused_parameters
            )

        # 只在主进程打印模型信息
        if (not self.use_ddp) or dist.get_rank() == 0:
            logger.info(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")

        self.global_step = 0

    def _forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """前向传播并计算损失和指标"""
        x = batch["features"].to(self.device, non_blocking=True)
        y = batch["label"].to(self.device, non_blocking=True)
        w = batch["weight"].to(self.device, non_blocking=True)

        pred = self.model(x)
        
        # 确保pred和y的形状匹配
        if pred.dim() == y.dim() - 1:
            pred = pred.unsqueeze(-1)

        out = {}
        out["loss"] = self.loss_fn(pred, y, w)
        
        # 计算所有指标
        for name, fn in self.metrics.items():
            out[name] = fn(pred, y, w)
        
        return out

    def fit(self, train_loader, valid_loader, *, num_epochs: int, save_path: str):
        """
        训练模型

        参数:
            train_loader: 训练数据加载器
            valid_loader: 验证数据加载器
            num_epochs: 训练轮数
            save_path: 模型保存路径
        """
        device = self.device
        is_main_process = (not self.use_ddp) or dist.get_rank() == 0

        for epoch in range(1, num_epochs + 1):
            # DDP 下重设 epoch（若 batch_sampler 支持）
            if self.use_ddp:
                if hasattr(train_loader.batch_sampler, "set_epoch"):
                    train_loader.batch_sampler.set_epoch(epoch)
                if hasattr(valid_loader.batch_sampler, "set_epoch"):
                    valid_loader.batch_sampler.set_epoch(epoch)

            # ---- 训练阶段 ----
            self.model.train()
            train_sums: Dict[str, torch.Tensor] = {}
            n_steps = 0

            for batch in train_loader:
                n_steps += 1
                outs = self._forward(batch)

                loss = outs["loss"] / self.grad_accum_steps

                # 梯度累积
                if (self.global_step % self.grad_accum_steps) == 0:
                    self.optimizer.zero_grad(set_to_none=True)

                loss.backward()

                if ((self.global_step + 1) % self.grad_accum_steps) == 0:
                    if self.grad_clip_norm is not None:
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip_norm)
                    self.optimizer.step()
                    if self.scheduler is not None:
                        self.scheduler.step()

                self.global_step += 1

                # 汇总指标
                for k, v in outs.items():
                    train_sums[k] = train_sums.get(k, torch.tensor(0.0, device=device)) + v.detach()

            # 计算平均值并进行DDP规约
            train_means = {k: ddp_mean_scalar(v / max(n_steps, 1)) for k, v in train_sums.items()}

            if is_main_process:
                log = " | ".join([f"{k}={float(val):.6f}" for k, val in train_means.items()])
                logger.info(f"[Epoch {epoch}] Train: {log}")

            # ---- 验证阶段 ----
            self.model.eval()
            valid_sums: Dict[str, torch.Tensor] = {}
            val_steps = 0

            with torch.no_grad():
                for batch in valid_loader:
                    val_steps += 1
                    outs = self._forward(batch)
                    for k, v in outs.items():
                        valid_sums[k] = valid_sums.get(k, torch.tensor(0.0, device=device)) + v.detach()

            valid_means = {k: ddp_mean_scalar(v / max(val_steps, 1)) for k, v in valid_sums.items()}

            if is_main_process:
                log = " | ".join([f"{k}={float(val):.6f}" for k, val in valid_means.items()])
                logger.info(f"[Epoch {epoch}] Valid: {log}")

            # ---- 早停检查 ----
            if self.primary_metric is not None:
                monitor = float(valid_means[self.primary_metric].item())
                core_model = self.model.module if isinstance(self.model, DDP) else self.model
                stop = self.early.step(monitor, core_model)

                # DDP环境下需要同步early stopping决策
                if self.use_ddp:
                    # 主进程决定是否停止，广播给所有进程
                    stop_tensor = torch.tensor(float(stop), device=device)
                    dist.broadcast(stop_tensor, src=0)
                    stop = bool(stop_tensor.item())
                
                if stop:
                    if is_main_process:
                        logger.info(f"[EarlyStop] best {self.primary_metric} = {self.early.best_value:.6f}")
                    break

        # 恢复最优模型并保存（仅主进程）
        core_model = self.model.module if isinstance(self.model, DDP) else self.model
        self.early.restore(core_model)

        if is_main_process:
            import os
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            torch.save(core_model.state_dict(), save_path)
            logger.info(f"Best model saved to {save_path}")

        # 训练结束后清理资源
        if self.use_ddp:
            # 确保所有进程同步
            dist.barrier()
