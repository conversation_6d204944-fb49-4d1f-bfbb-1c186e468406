"""
测试新的 Tensor 化数据集类

验证数据集能正确加载数据并返回预期的 dict 格式。
"""

import numpy as np
import torch
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    TensorPairDataset,
    TensorMinuteLookbackWithinDayDataset,
    TensorMinuteLookbackAcrossDaysDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict
)


def load_test_data():
    """加载测试数据"""
    data_path = "projs/stock1m/data/OHLCVA_Vwap_cube.npy"
    X = np.load(data_path)  # [F=8, D=61, T=241, N=6000]
    print(f"加载数据形状: {X.shape}")
    print(f"数据类型: {X.dtype}")
    print(f"NaN数量: {np.isnan(X).sum()}")
    return X


def test_field_spec():
    """测试 FieldSpec 功能"""
    print("\n=== 测试 FieldSpec ===")
    
    # 测试无权重情况：7个特征 + 1个标签
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    
    # 创建测试数据 [N, T, F_total=8]
    test_data = np.random.randn(100, 241, 8).astype(np.float32)
    
    f_np, w_np, y_np = spec.split_np(test_data)
    print(f"特征形状: {f_np.shape}")  # 应该是 [100, 241, 7]
    print(f"权重形状: {w_np.shape}")  # 应该是全1权重
    print(f"标签形状: {y_np.shape}")  # 应该是 [100, 241, 1]
    
    # 转换为 tensor
    f, w, y = spec.to_tensor(f_np, w_np, y_np)
    print(f"特征 tensor 形状: {f.shape}, 类型: {f.dtype}")
    print(f"权重 tensor 形状: {w.shape}, 类型: {w.dtype}")
    print(f"标签 tensor 形状: {y.shape}, 类型: {y.dtype}")
    
    # 测试有权重情况：6个特征 + 1个权重 + 1个标签
    spec_with_weight = FieldSpec(n_feat=6, n_label=1, n_weight=1)
    f_np2, w_np2, y_np2 = spec_with_weight.split_np(test_data)
    print(f"\n带权重 - 特征形状: {f_np2.shape}")  # [100, 241, 6]
    print(f"带权重 - 权重形状: {w_np2.shape}")    # [100, 241, 1]
    print(f"带权重 - 标签形状: {y_np2.shape}")    # [100, 241, 1]
    
    return spec, spec_with_weight


def test_tensor_day_section_dataset():
    """测试 TensorDaySectionDataset"""
    print("\n=== 测试 TensorDaySectionDataset ===")
    X = load_test_data()
    cube = MinuteCube.from_fdtN(X)
    
    # 7个特征 + 1个标签
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    dataset = TensorDaySectionDataset(cube, spec)
    
    print(f"数据集长度: {len(dataset)}")
    
    # 测试单个样本
    sample = dataset[0]
    print(f"样本键: {sample.keys()}")
    print(f"特征形状: {sample['features'].shape}")
    print(f"标签形状: {sample['label'].shape}")
    print(f"权重形状: {sample['weight'].shape}")
    print(f"特征数据类型: {sample['features'].dtype}")
    
    # 测试 DataLoader（变长样本，需要自定义采样器）
    # 模拟单进程环境
    sampler = DistributedSingleIndexBatchSampler(
        num_samples=len(dataset),
        num_replicas=1,  # 单进程
        rank=0,
        shuffle=False
    )
    loader = DataLoader(
        dataset, 
        batch_sampler=sampler, 
        collate_fn=passthrough_collate_dict
    )
    
    print(f"DataLoader长度: {len(loader)}")
    
    # 测试迭代
    for i, batch in enumerate(loader):
        print(f"批次 {i}: 特征形状={batch['features'].shape}, 标签形状={batch['label'].shape}")
        if i >= 2:  # 只测试前3个批次
            break
    
    return dataset


def test_tensor_pair_dataset():
    """测试 TensorPairDataset"""
    print("\n=== 测试 TensorPairDataset ===")
    X = load_test_data()
    cube = MinuteCube.from_fdtN(X)
    
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    dataset = TensorPairDataset(cube, spec)
    
    print(f"数据集长度: {len(dataset)}")
    
    # 测试单个样本
    sample = dataset[0]
    print(f"样本键: {sample.keys()}")
    print(f"特征形状: {sample['features'].shape}")
    print(f"标签形状: {sample['label'].shape}")
    print(f"权重形状: {sample['weight'].shape}")
    
    # 测试 DataLoader（固定形状，可以正常batch）
    # 可以使用官方 DistributedSampler 或普通 DataLoader
    loader = DataLoader(dataset, batch_size=4, shuffle=False)
    
    for i, batch in enumerate(loader):
        print(f"批次 {i}: 特征形状={batch['features'].shape}, 标签形状={batch['label'].shape}")
        if i >= 2:
            break
    
    return dataset


def test_with_standardizer():
    """测试与标准化器的结合使用"""
    print("\n=== 测试与标准化器结合 ===")
    X = load_test_data()
    cube_all = MinuteCube.from_fdtN(X)
    
    # 拆分和标准化
    cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
    std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
    cube_train_std = std.transform(cube_train)
    cube_valid_std = std.transform(cube_valid)
    
    # 创建数据集
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    ds_train = TensorDaySectionDataset(cube_train_std, spec)
    ds_valid = TensorDaySectionDataset(cube_valid_std, spec)
    
    print(f"训练集长度: {len(ds_train)}")
    print(f"验证集长度: {len(ds_valid)}")
    
    # 测试一个批次
    sample = ds_train[0]
    features = sample['features']
    print(f"标准化后特征范围: [{features.min():.3f}, {features.max():.3f}]")
    print(f"标准化后特征均值: {features.mean():.6f}")
    print(f"标准化后特征标准差: {features.std():.6f}")
    
    return ds_train, ds_valid


def test_with_weight():
    """测试带权重的情况"""
    print("\n=== 测试带权重 ===")
    X = load_test_data()
    cube = MinuteCube.from_fdtN(X)
    
    # 6个特征 + 1个权重 + 1个标签
    spec = FieldSpec(n_feat=6, n_label=1, n_weight=1)
    dataset = TensorDaySectionDataset(cube, spec)
    
    sample = dataset[0]
    print(f"带权重的特征形状: {sample['features'].shape}")
    print(f"权重形状: {sample['weight'].shape}")
    print(f"标签形状: {sample['label'].shape}")
    print(f"特征维度应该是 6 (排除权重和标签)")


def test_minute_lookback_datasets():
    """测试分钟回看数据集"""
    print("\n=== 测试分钟回看数据集 ===")
    X = load_test_data()
    cube = MinuteCube.from_fdtN(X)
    
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    L = 16  # 回看16分钟
    
    # 测试日内回看
    ds_within = TensorMinuteLookbackWithinDayDataset(cube, spec, L=L)
    sample_within = ds_within[0]
    print(f"日内回看 - 特征形状: {sample_within['features'].shape}")
    print(f"日内回看 - 标签形状: {sample_within['label'].shape}")
    
    # 测试跨天回看
    ds_across = TensorMinuteLookbackAcrossDaysDataset(cube, spec, L=L, filter_mode="anchor")
    sample_across = ds_across[0]
    print(f"跨天回看 - 特征形状: {sample_across['features'].shape}")
    print(f"跨天回看 - 标签形状: {sample_across['label'].shape}")


if __name__ == "__main__":
    print("开始测试新的 Tensor 化数据集类...")
    
    try:
        # 基础测试
        test_field_spec()
        
        # 各种数据集测试
        test_tensor_day_section_dataset()
        test_tensor_pair_dataset()
        
        # 与标准化器结合
        test_with_standardizer()
        
        # 权重测试
        test_with_weight()
        
        # 分钟回看测试
        test_minute_lookback_datasets()
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
