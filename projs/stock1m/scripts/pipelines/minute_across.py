"""
MinuteAcross Pipeline - 跨天回看预测

适用场景：
- 长期依赖建模、连续预测
- 输出: [N_sel, L, F] -> [N_sel, 1]
- 回看窗口可跨天，支持全局操作
- 使用point预测模型
"""

import os
import sys
import argparse
import numpy as np
import torch
import torch.nn as nn
from dataclasses import dataclass
from loguru import logger
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from DLlib.data.cube import MinuteCube
from DLlib.data.field_spec import FieldSpec
from DLlib.data.standardizer import CubeStandardizer
from DLlib.data.datasets import TensorMinuteLookbackAcrossDaysDataset
from DLlib.data.samplers import DistributedSingleIndexBatchSampler
from DLlib.data.collate import passthrough_collate_dict
from DLlib.data.ddp_utils import set_global_seed, setup_dist, cleanup_dist, is_main_process
from DLlib.data.folds import rolling_splits_by_years, get_fold_info

from DLlib.train import ModelTrainer, VectorLoss, rmse, ic_cs, WandbTracker


@dataclass
class Config:
    """MinuteAcross Pipeline 配置"""
    # 实验名称
    exp_name: str = f"test{time.strftime('%m%d')}"

    # 数据配置
    data_path: str = "/disk4/shared/intern/laiyc/forModel/alpha20.npy"
    n_feat: int = 20
    n_label: int = 1
    n_weight: int = 0

    fillna_features_to_zero: bool = True
    features_norm: str = "ts" # for MinuteAcross, "cs" | "ts" | "none"
    labels_norm: str = "cs"

    # 滚动配置（61天小样本，2天=1年）
    train_years: float = 3.0   # 6天训练
    test_years: float = 1.0    # 2天测试
    days_per_year: int = 240     
    inner_val_ratio: float = 0.2

    # 回看配置
    lookback_minutes: int = 90  # 回看90分钟
    filter_mode: str = "window"  # "anchor" | "window" | "none"
    pad_mode: str = "repeat"     # "repeat" | "nan"

    # 模型配置
    hidden: int = 256
    layers: int = 1
    dropout: float = 0.1

    # 训练配置
    lr: float = 1e-4
    weight_decay: float = 1e-4
    clip: float = 1.0
    epochs: int = 1
    early_stop: int = 3

    # 损失和指标配置
    loss_type: str = "mse"
    primary_metric: str = "ic"
    primary_higher_better: bool = True

    # 系统配置
    seed: int = 666
    num_workers: int = 8
    accum_steps: int = 1
    ckpt_root: str = "projs/stock1m/checkpoints"

    # Wandb配置
    wandb_project: str = "tslib"

    wandb_group: str = "stock1m_minute"
    wandb_mode: str = "online"
    wandb_dir: str = "projs/stock1m/logs/wandb"
    wandb_tags: list = None

    # 模型信息
    model_name: str = "GRUPoint"
    model_type: str = "point"

    def __post_init__(self):
        if self.wandb_tags is None:
            self.wandb_tags = ["MinuteLookback", "GRU", "point", "跨天回看"]


class GRUPoint(nn.Module):
    """Point预测GRU模型"""
    
    def __init__(self, f_in: int, hidden: int = 256, layers: int = 2, dropout: float = 0.1):
        super().__init__()
        self.gru = nn.GRU(
            input_size=f_in, 
            hidden_size=hidden, 
            num_layers=layers, 
            batch_first=True, 
            bidirectional=False,
            dropout=dropout if layers > 1 else 0
        )
        self.head = nn.Linear(hidden, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: [N, L, F]
        
        返回:
            [N, 1]
        """
        out, _ = self.gru(x)  # [N, L, hidden]
        last_out = out[:, -1, :]  # [N, hidden] 取最后一个时间步
        last_out = self.dropout(last_out)
        return self.head(last_out)  # [N, 1]


def run(args=None):
    """运行MinuteAcross Pipeline"""
    cfg = Config()
    if args is not None and args.data_path:
        cfg.data_path = args.data_path

    # 设置全局随机种子
    set_global_seed(cfg.seed)

    # 设置分布式环境
    _, device = setup_dist()

    # 加载数据
    X = np.load(cfg.data_path, mmap_mode="r")  # [F, D, T, N]
    F, D, T, N = X.shape
    
    if is_main_process():
        logger.info(f"=== MinuteAcross Pipeline ===")
        logger.info(f"数据形状: F={F}, D={D}, T={T}, N={N}")
        logger.info(f"回看配置: {cfg.lookback_minutes}分钟, 过滤模式: {cfg.filter_mode}")

    # 生成滚动切分
    folds = list(rolling_splits_by_years(
        D=D, 
        train_years=cfg.train_years, 
        test_years=cfg.test_years,
        days_per_year=cfg.days_per_year, 
        inner_val_ratio=cfg.inner_val_ratio,
    ))
    
    if is_main_process():
        fold_info = get_fold_info(D, cfg.train_years, cfg.test_years, cfg.days_per_year)
        logger.info(f"滚动配置: {cfg.train_years}年训练 + {cfg.test_years}年测试")
        logger.info(f"Fold信息: {fold_info}")

    spec = FieldSpec(n_feat=cfg.n_feat, n_label=cfg.n_label, n_weight=cfg.n_weight)

    # 初始化Wandb跟踪器
    tracker = WandbTracker(
        project=cfg.wandb_project,
        run_name=f"{cfg.exp_name}-minute-GRU-rolling-{cfg.train_years}x{cfg.test_years}-L{cfg.lookback_minutes}",
        group=cfg.wandb_group,
        mode=cfg.wandb_mode,
        dir_=cfg.wandb_dir,
        tags=["MinuteLookback", "GRU", "point", "跨天回看"],
        config={
            "pipeline": "MinuteAcross",
            "model": "GRUPoint", 
            "hidden": cfg.hidden, 
            "layers": cfg.layers,
            "dropout": cfg.dropout,
            "lookback_minutes": cfg.lookback_minutes,
            "filter_mode": cfg.filter_mode,
            "pad_mode": cfg.pad_mode,
            "optim.lr": cfg.lr, 
            "optim.wd": cfg.weight_decay,
            "clip": cfg.clip, 
            "epochs": cfg.epochs,
            "rolling": f"{cfg.train_years}x{cfg.test_years} years, val_ratio={cfg.inner_val_ratio}",
            "data_shape": [F, D, T, N],
            "num_folds": len(folds),
        },
    )

    # 开始滚动训练
    fold_results = []  # 存储所有fold的结果
    for fid, (idx_tr, idx_va, idx_te) in enumerate(folds):
        if is_main_process():
            logger.info(f"[Fold {fid}] 开始训练")
            logger.info(f"  训练: {len(idx_tr)}天, 验证: {len(idx_va)}天, 测试: {len(idx_te)}天")

        # 1. 切分数据
        X_tr = X[:, idx_tr, :, :]
        X_va = X[:, idx_va, :, :]
        X_te = X[:, idx_te, :, :]

        # 2. 构造cube并标准化
        cube_tr = MinuteCube.from_fdtN(X_tr)
        cube_va = MinuteCube.from_fdtN(X_va)
        cube_te = MinuteCube.from_fdtN(X_te)

        # # lookback型不做标准化
        # std = CubeStandardizer(mode="per_stock_f").fit(cube_tr)

        # cube_tr = std.transform(cube_tr)
        # cube_va = std.transform(MinuteCube.from_fdtN(X_va))
        # cube_te = std.transform(MinuteCube.from_fdtN(X_te))

        #cube_va = MinuteCube.from_fdtN(X_va)
        #cube_te = MinuteCube.from_fdtN(X_te)

        # 3. 创建数据集和采样器
        ds_tr = TensorMinuteLookbackAcrossDaysDataset(
            cube_tr, spec,
            L=cfg.lookback_minutes,
            filter_mode=cfg.filter_mode,
            pad_mode=cfg.pad_mode, 
            fillna_features_to_zero=cfg.fillna_features_to_zero, 
            features_norm=cfg.features_norm, 
            labels_norm=cfg.labels_norm
        )
        
        ds_va = TensorMinuteLookbackAcrossDaysDataset(
            cube_va, spec,
            L=cfg.lookback_minutes,
            filter_mode=cfg.filter_mode,
            pad_mode=cfg.pad_mode, 
            fillna_features_to_zero=cfg.fillna_features_to_zero, 
            features_norm=cfg.features_norm, 
            labels_norm=cfg.labels_norm
        )

        ds_te = TensorMinuteLookbackAcrossDaysDataset(
            cube_te, spec,
            L=cfg.lookback_minutes,
            filter_mode=cfg.filter_mode,
            pad_mode=cfg.pad_mode, 
            fillna_features_to_zero=cfg.fillna_features_to_zero, 
            features_norm=cfg.features_norm, 
            labels_norm=cfg.labels_norm
        )

        bs_tr = DistributedSingleIndexBatchSampler(len(ds_tr), shuffle=True, drop_last=False)
        bs_va = DistributedSingleIndexBatchSampler(len(ds_va), shuffle=False, drop_last=False)
        bs_te = DistributedSingleIndexBatchSampler(len(ds_te), shuffle=False, drop_last=False)

        dl_tr = torch.utils.data.DataLoader(
            ds_tr, batch_sampler=bs_tr, collate_fn=passthrough_collate_dict,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )
        dl_va = torch.utils.data.DataLoader(
            ds_va, batch_sampler=bs_va, collate_fn=passthrough_collate_dict,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )
        dl_te = torch.utils.data.DataLoader(
            ds_te, batch_sampler=bs_te, collate_fn=passthrough_collate_dict,
            num_workers=cfg.num_workers, pin_memory=True, persistent_workers=True
        )

        # 4. 创建模型和优化器
        model = GRUPoint(cfg.n_feat, cfg.hidden, cfg.layers, cfg.dropout).to(device)
        optimizer = torch.optim.Adam(model.parameters(), lr=cfg.lr, weight_decay=cfg.weight_decay)

        # 5. 定义损失函数和指标（point预测，IC无需T_idx）
        loss_fn = VectorLoss(cfg.loss_type, pred_dim=1, label_dim=1)
        metrics = {
            # "rmse": lambda p, y, w: rmse(p, y, w),
            "ic": lambda p, y, _: ic_cs(p, y, mode="spearman_approx"),  # 无需T_idx
        }

        # 6. 创建训练器
        trainer = ModelTrainer(
            model=model,
            optimizer=optimizer,
            loss_fn=loss_fn,
            metrics=metrics,
            primary_metric=cfg.primary_metric,
            primary_higher_better=cfg.primary_higher_better,
            device=device,
            use_ddp=True,
            grad_clip_norm=cfg.clip,
            grad_accum_steps=cfg.accum_steps,
            scheduler=None,
            early_stop_patience=cfg.early_stop,
            tracker=tracker,
        )

        # 7. 训练模型
        fold_dir = os.path.join(cfg.ckpt_root, f"minute_fold{fid:02d}")
        os.makedirs(fold_dir, exist_ok=True)
        best_path = os.path.join(fold_dir, "best.pt")

        trainer.fit(dl_tr, dl_va, num_epochs=cfg.epochs, save_path=best_path)

        # 8. 测试阶段（多卡分布式预测）
        # 加载最佳模型到所有进程
        trainer.model.load_state_dict(torch.load(best_path, map_location=device))

        # 获取测试集的维度信息
        D_te = cube_te.D                  # 测试天数
        T    = cube_te.T                  # 每天分钟数
        N    = cube_te.N                  # 总股票数
        Fl   = spec.n_label              # 标签通道数，通常为1

        if is_main_process():
            logger.info(f"[Fold {fid}] 开始测试...")

        test_metrics = trainer.predict_ddp(
            data_loader=dl_te,
            cube_info={"D": D_te, "N": N, "T": T, "Fl": Fl},
            save_dir=fold_dir,
            metrics=metrics,
            primary_metric=cfg.primary_metric,
        )

        if is_main_process():
            # 记录单个fold结果
            tracker.log_metrics({f"fold_{fid}": test_metrics.get(cfg.primary_metric, 0.0)})
            tracker.save_artifact(os.path.join(fold_dir, "test_preds.npy"), type_="predictions")

            # 存储fold结果用于最终汇总
            fold_results.append(test_metrics.get(cfg.primary_metric, 0.0))

            # 输出主要指标
            main_metric_value = test_metrics.get(cfg.primary_metric, "N/A")
            logger.info(f"[Fold {fid}] 完成 - 测试{cfg.primary_metric.upper()}: {main_metric_value:.6f}")

    # 记录整体汇总结果
    if fold_results and is_main_process():
        cv_mean = sum(fold_results) / len(fold_results)
        cv_std = (sum((x - cv_mean) ** 2 for x in fold_results) / len(fold_results)) ** 0.5
        tracker.log_metrics({
            "cv_mean": cv_mean,
            "cv_std": cv_std,
            "cv_folds": len(fold_results)
        })
        logger.info(f"CV总结 - 平均{cfg.primary_metric.upper()}: {cv_mean:.6f} ± {cv_std:.6f}")

    # 结束实验
    tracker.finish()
    cleanup_dist()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MinuteAcross Pipeline")
    parser.add_argument("--data_path", type=str, default="/disk4/shared/intern/laiyc/forModel/alpha20.npy")
    args = parser.parse_args()
    run(args)
