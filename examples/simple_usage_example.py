"""
简单使用示例

展示如何使用新的数据集类进行基本的数据加载和处理。
"""

import numpy as np
import torch
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    TensorPairDataset,
    TensorMinuteLookbackWithinDayDataset,
    TensorMinuteLookbackAcrossDaysDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict
)


def main():
    """主函数：展示基本用法"""
    print("=== 数据集使用示例 ===")
    
    # === 1. 数据准备 ===
    print("\n1. 加载和准备数据...")
    X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")  # [F=8, D=61, T=241, N=6000]
    print(f"原始数据形状: {X.shape}")
    
    # 创建 MinuteCube
    cube_all = MinuteCube.from_fdtN(X)
    print(f"Cube: D={cube_all.D}, T={cube_all.T}, N={cube_all.N}, F={cube_all.F}")
    
    # 拆分训练/验证集
    cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
    print(f"训练集天数: {cube_train.D}, 验证集天数: {cube_valid.D}")
    
    # === 2. 标准化 ===
    print("\n2. 数据标准化...")
    std = CubeStandardizer(mode="per_stock_f", eps=1e-6)
    std.fit(cube_train)
    cube_train_std = std.transform(cube_train)
    cube_valid_std = std.transform(cube_valid)
    print("标准化完成")
    
    # === 3. 定义通道规格 ===
    print("\n3. 定义通道规格...")
    # 7个特征 + 1个标签（无权重）
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
    print(f"通道规格: {spec.n_feat}特征 + {spec.n_weight}权重 + {spec.n_label}标签")
    
    # === 4. 创建不同类型的数据集 ===
    print("\n4. 创建数据集...")
    
    # 4.1 DaySection 数据集（变长样本）
    ds_day = TensorDaySectionDataset(cube_train_std, spec)
    print(f"DaySection 数据集长度: {len(ds_day)}")
    
    sample_day = ds_day[0]
    print(f"DaySection 样本 - 特征: {sample_day['features'].shape}, 标签: {sample_day['label'].shape}, 权重: {sample_day['weight'].shape}")
    
    # 4.2 Pair 数据集（固定形状）
    ds_pair = TensorPairDataset(cube_train_std, spec)
    print(f"Pair 数据集长度: {len(ds_pair)}")
    
    sample_pair = ds_pair[0]
    print(f"Pair 样本 - 特征: {sample_pair['features'].shape}, 标签: {sample_pair['label'].shape}, 权重: {sample_pair['weight'].shape}")
    
    # 4.3 分钟回看数据集
    ds_lookback = TensorMinuteLookbackWithinDayDataset(cube_train_std, spec, L=16)
    print(f"分钟回看数据集长度: {len(ds_lookback)}")
    
    sample_lookback = ds_lookback[0]
    print(f"分钟回看样本 - 特征: {sample_lookback['features'].shape}, 标签: {sample_lookback['label'].shape}, 权重: {sample_lookback['weight'].shape}")
    
    # === 5. 创建 DataLoader ===
    print("\n5. 创建 DataLoader...")
    
    # 5.1 变长样本：使用自定义分布式采样器
    sampler_day = DistributedSingleIndexBatchSampler(
        num_samples=len(ds_day),
        num_replicas=1,  # 单进程模拟
        rank=0,
        shuffle=True
    )
    
    loader_day = DataLoader(
        ds_day,
        batch_sampler=sampler_day,
        collate_fn=passthrough_collate_dict,
        num_workers=0  # 简化示例
    )
    
    # 5.2 固定形状：使用普通 DataLoader
    loader_pair = DataLoader(
        ds_pair,
        batch_size=32,
        shuffle=True,
        num_workers=0
    )
    
    # === 6. 数据迭代示例 ===
    print("\n6. 数据迭代示例...")
    
    print("DaySection DataLoader:")
    for i, batch in enumerate(loader_day):
        print(f"  批次 {i}: 特征 {batch['features'].shape}, 标签 {batch['label'].shape}")
        if i >= 2:
            break
    
    print("\nPair DataLoader:")
    for i, batch in enumerate(loader_pair):
        print(f"  批次 {i}: 特征 {batch['features'].shape}, 标签 {batch['label'].shape}")
        if i >= 2:
            break
    
    # === 7. 带权重的示例 ===
    print("\n7. 带权重的示例...")
    spec_with_weight = FieldSpec(n_feat=6, n_label=1, n_weight=1)
    ds_weighted = TensorDaySectionDataset(cube_train_std, spec_with_weight)
    sample_weighted = ds_weighted[0]
    print(f"带权重样本 - 特征: {sample_weighted['features'].shape}, 权重: {sample_weighted['weight'].shape}, 标签: {sample_weighted['label'].shape}")
    
    # === 8. 验证权重值 ===
    print("\n8. 验证权重值...")
    # 无权重时应该是全1
    weight_no_weight = sample_day['weight']
    print(f"无权重时的权重值范围: [{weight_no_weight.min():.1f}, {weight_no_weight.max():.1f}]")
    print(f"无权重时的权重均值: {weight_no_weight.mean():.1f}")
    
    # 有权重时应该是实际数据
    weight_with_weight = sample_weighted['weight']
    print(f"有权重时的权重值范围: [{weight_with_weight.min():.3f}, {weight_with_weight.max():.3f}]")
    print(f"有权重时的权重均值: {weight_with_weight.mean():.3f}")
    
    print("\n✅ 示例完成！")


if __name__ == "__main__":
    main()
