# MLlib/utils/flatten.py
from __future__ import annotations
import os, numpy as np
from typing import Dict, Optional

def _safe_std(std: np.ndarray, eps: float) -> np.ndarray:
    """安全的标准差计算，避免除零和非有限值"""
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)

def zscore_y_cs(y: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
    标签做截面 zscore（沿 N），用 nanmean/nanstd；原始 NaN 保留为 NaN。
      适配 y 为 [N, T, d_y] 或 [N, 1, d_y]
    """
    mask = ~np.isfinite(y)                              # True 表示原先是 NaN/Inf
    mu = np.nanmean(y, axis=0, keepdims=True)
    sd = np.nanstd (y, axis=0, keepdims=True)
    sd = _safe_std(sd, eps)
    out = (y - mu) / sd
    out[mask] = np.nan
    return out



def build_flat_samples_from_X(
    X_fdtN: np.ndarray,
    *,
    n_feat: int,
    n_weight: int,
    n_label: int,
    day_idx: np.ndarray,
    drop_nan_y: bool = True,
) -> Dict[str, np.ndarray]:
    """
    从 X[F,D,T,N] 生成扁平表格样本（简化版本）
    
    参数:
        X_fdtN: 原始数据，形状为 [F, D, T, N]（可以是mmap数组）
        n_feat: 特征数量
        n_weight: 权重数量
        n_label: 标签数量
        day_idx: 选择的天索引
        drop_nan_y: 是否丢弃NaN标签样本
        
    返回:
        Dict包含: X: [M,F_feat], y: [M], d/t/n: [M]
        
    说明:
        - 仅对 y 进行截面 zscore（沿 N）；特征不做任何标准化
        - drop_nan_y=True 时丢弃 NaN 标签样本
        - 直接在内存中处理，依赖X_fdtN的mmap特性节省内存
    """
    F, D, T, N = X_fdtN.shape
    assert n_feat + n_weight + n_label == F, f"特征维度不匹配: {n_feat}+{n_weight}+{n_label} != {F}"
    
    day_idx = np.asarray(day_idx, dtype=np.int64)
    Dp = len(day_idx)

    # 直接处理，利用mmap的按需加载特性
    X_sub = X_fdtN[:, day_idx, :, :]                       # [F,D',T,N] - mmap按需加载
    X_dtnf = np.moveaxis(X_sub, (0,1,2,3), (3,0,1,2))      # [D',T,N,F]
    
    # 提取特征和标签
    f = X_dtnf[..., :n_feat]                                # [D',T,N,Ff]
    y = X_dtnf[..., n_feat + n_weight : n_feat + n_weight + n_label][..., 0]  # [D',T,N]

    # 对y进行截面zscore
    y = zscore_y_cs(y)                                      # [D',T,N]
    
    # 展平为表格格式
    X_flat = f.reshape(-1, n_feat).astype(np.float32)      # [D'*T*N, n_feat]
    y_flat = y.reshape(-1).astype(np.float32)              # [D'*T*N]
    
    # 构建索引数组
    d_grid = np.repeat(np.arange(Dp), T*N).astype(np.int32)           # day相对索引
    t_grid = np.tile(np.repeat(np.arange(T), N), Dp).astype(np.int32) # minute索引
    n_grid = np.tile(np.arange(N), Dp*T).astype(np.int32)             # stock索引

    if drop_nan_y:
        mask = np.isfinite(y_flat)
        return {
            "X": X_flat[mask],
            "y": y_flat[mask],
            "d": d_grid[mask],
            "t": t_grid[mask],
            "n": n_grid[mask],
        }
    else:
        return {"X": X_flat, "y": y_flat, "d": d_grid, "t": t_grid, "n": n_grid}