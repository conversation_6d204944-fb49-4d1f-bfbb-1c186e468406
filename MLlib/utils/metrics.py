# MLlib/utils/metrics.py
import numpy as np
from typing import Sequence
from collections import defaultdict

def rank_ic_focus(
    preds: np.ndarray,
    y_true: np.ndarray,
    d: np.ndarray,
    t: np.ndarray,
    T_focus: Sequence[int],
) -> float:
    """
    对每个 (d,t) 且 t∈T_focus 计算 Spearman 近似（两次 argsort）并平均
    
    参数:
        preds: 预测值数组 [N]
        y_true: 真实标签数组 [N] 
        d: 天索引数组 [N]
        t: 分钟索引数组 [N]
        T_focus: 关注的分钟索引列表
        
    返回:
        平均IC值，如果无有效计算则返回NaN
    """
    Tset = set(int(x) for x in T_focus)

    def _spearman_approx(u, v) -> float:
        """Spearman相关系数的近似计算（两次argsort）"""
        if u.size < 3:
            return np.nan
        ru = u.argsort().argsort().astype(np.float64)
        rv = v.argsort().argsort().astype(np.float64)
        ru -= ru.mean()
        rv -= rv.mean()
        denom = np.sqrt((ru**2).sum() * (rv**2).sum()) + 1e-12
        return float((ru * rv).sum() / denom)

    # 按(d,t)分组收集预测值和真值
    bucket_pred = defaultdict(list)
    bucket_true = defaultdict(list)
    
    for i in range(len(preds)):
        tt = int(t[i])
        if tt in Tset:
            key = (int(d[i]), tt)
            bucket_pred[key].append(preds[i])
            bucket_true[key].append(y_true[i])

    # 计算每个截面的IC
    cors = []
    for k in bucket_pred:
        u = np.asarray(bucket_pred[k], dtype=np.float64)
        v = np.asarray(bucket_true[k], dtype=np.float64)
        
        # 过滤掉NaN值
        mask = np.isfinite(v)
        u, v = u[mask], v[mask]
        
        c = _spearman_approx(u, v)
        if np.isfinite(c):
            cors.append(c)
    
    return float(np.mean(cors)) if len(cors) else float("nan")